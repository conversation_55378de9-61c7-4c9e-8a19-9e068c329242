# Tailscale状态查看工具

一个用于查看Tailscale网络设备状态的Web工具，可以调用本地终端执行`tailscale status`命令并以美观的表格形式显示结果。

## 功能特性

- 🔗 **实时状态查看**: 调用本地`tailscale status`命令获取最新设备状态
- 📊 **表格化显示**: 将命令行输出解析为易读的表格格式
- 🎨 **美观界面**: 现代化的响应式设计，支持中文界面
- 🚀 **一键执行**: 点击按钮即可执行命令
- 📱 **状态指示**: 用颜色区分设备在线/离线状态
- 💻 **跨平台**: 支持Windows、Linux、macOS

## 文件说明

- `tailscale-status.html` - 主要的Web界面文件
- `tailscale-server.py` - 本地HTTP服务器，用于调用系统命令
- `start-tailscale-server.bat` - Windows批处理启动脚本
- `README-Tailscale.md` - 本说明文件

## 使用方法

### 方法一：使用本地服务器（推荐）

1. **启动服务器**
   - Windows: 双击运行 `start-tailscale-server.bat`
   - 其他系统: 运行 `python tailscale-server.py`

2. **打开网页**
   - 服务器启动后会自动打开浏览器
   - 或手动打开 `tailscale-status.html` 文件

3. **执行命令**
   - 点击"🚀 执行 tailscale status"按钮
   - 等待命令执行完成
   - 查看结果表格

### 方法二：仅查看演示界面

直接在浏览器中打开 `tailscale-status.html` 文件，将显示模拟数据。

## 系统要求

### 必需组件
- **Python 3.6+**: 用于运行本地服务器
- **现代浏览器**: Chrome、Firefox、Safari、Edge等

### 可选组件
- **Tailscale**: 如需调用真实命令，请确保已安装并添加到系统PATH

## 安装Tailscale

如果尚未安装Tailscale，请访问官网下载：
- 官网: https://tailscale.com/download
- Windows: 下载MSI安装包
- Linux: 使用包管理器安装
- macOS: 下载DMG文件或使用Homebrew

## 显示内容说明

工具会显示以下信息：

### 原始输出
显示`tailscale status`命令的完整输出，保持原始格式。

### 设备状态表
解析后的表格包含以下列：
- **IP地址**: Tailscale分配的内网IP
- **设备名称**: 设备的主机名
- **用户**: Tailscale账户用户名
- **系统**: 操作系统类型
- **状态**: 设备连接状态
  - 🟢 在线 (active)
  - 🔴 离线 (offline)  
  - 🔵 当前设备 (-)
- **连接信息**: 直连信息、流量统计等

## 状态说明

- **在线 (active)**: 设备当前在线并可访问
- **离线 (offline)**: 设备当前离线或不可访问
- **当前设备 (-)**: 运行命令的当前设备

## 故障排除

### 常见问题

1. **"未找到tailscale命令"**
   - 确保已安装Tailscale
   - 检查Tailscale是否添加到系统PATH
   - 重启终端或重新登录

2. **"API调用失败"**
   - 确保本地服务器正在运行
   - 检查端口8080是否被占用
   - 尝试使用其他端口：`python tailscale-server.py 8081`

3. **页面显示模拟数据**
   - 这是正常的，表示无法连接到本地服务器
   - 启动 `tailscale-server.py` 即可使用真实数据

4. **Python相关错误**
   - 确保Python版本为3.6或更高
   - 检查Python是否正确安装并添加到PATH

### 端口配置

默认使用端口8080，如需更改：
```bash
python tailscale-server.py 8081
```

### 日志查看

服务器运行时会显示详细的访问日志，有助于诊断问题。

## 技术实现

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **后端**: Python 3 + http.server
- **命令调用**: subprocess模块
- **跨域支持**: CORS头设置
- **数据格式**: JSON API

## 安全说明

- 本工具仅在本地运行，不会向外部发送数据
- HTTP服务器仅监听localhost，外部无法访问
- 建议仅在可信环境中使用

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持基本的tailscale status查看
- 美观的Web界面
- 本地服务器支持

## 许可证

本工具为开源项目，仅供学习和个人使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 发送邮件

---

**注意**: 本工具需要Tailscale正确安装和配置才能显示真实数据。如果只是查看界面效果，可以直接打开HTML文件查看模拟数据。
