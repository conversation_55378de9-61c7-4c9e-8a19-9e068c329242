@echo off
chcp 65001 >nul
title Tailscale状态查看服务器

echo.
echo ========================================
echo    Tailscale状态查看工具服务器启动器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查tailscale是否安装
tailscale version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  警告: 未找到Tailscale命令
    echo 请确保已安装Tailscale并添加到系统PATH
    echo 下载地址: https://tailscale.com/download
    echo.
    echo 继续启动服务器（将使用模拟数据）...
    echo.
) else (
    echo ✅ 检测到Tailscale已安装
    echo.
)

REM 切换到脚本所在目录
cd /d "%~dp0"

echo 🚀 正在启动服务器...
echo.

REM 启动Python服务器
python tailscale-server.py

echo.
echo 服务器已停止
pause
