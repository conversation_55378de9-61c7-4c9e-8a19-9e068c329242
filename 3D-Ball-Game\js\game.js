// 3D 小球滚动模型 - 游戏主逻辑
// 使用 Three.js 创建 3D 场景和 Cannon.js 处理物理效果

// 全局变量
let scene, camera, renderer, world;
let ball, ballBody;
let tracks = [];
let trackBodies = [];
let controls;
let keys = {};
let gameStarted = false;
let canJump = false;
let clock = new THREE.Clock();
let startPosition = new THREE.Vector3(0, 5, 0);
let checkpoints = [];
let currentCheckpoint = 0;

// 初始化函数
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87CEEB); // 天蓝色背景

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 10, 20);

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    document.body.appendChild(renderer.domElement);

    // 添加轨道控制器
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;

    // 创建物理世界
    world = new CANNON.World();
    world.gravity.set(0, -9.82, 0); // 设置重力
    world.broadphase = new CANNON.NaiveBroadphase();
    world.solver.iterations = 10;

    // 添加光源
    addLights();

    // 创建小球
    createBall();

    // 创建复杂轨道
    createTracks();

    // 添加事件监听器
    window.addEventListener('resize', onWindowResize);
    window.addEventListener('keydown', onKeyDown);
    window.addEventListener('keyup', onKeyUp);
    document.getElementById('restart').addEventListener('click', restartGame);

    // 开始游戏循环
    animate();
}

// 添加光源
function addLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    // 方向光（模拟太阳光）
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(100, 100, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -100;
    directionalLight.shadow.camera.right = 100;
    directionalLight.shadow.camera.top = 100;
    directionalLight.shadow.camera.bottom = -100;
    scene.add(directionalLight);
}

// 创建小球
function createBall() {
    // Three.js 几何体
    const radius = 0.5;
    const geometry = new THREE.SphereGeometry(radius, 32, 32);
    const material = new THREE.MeshStandardMaterial({ 
        color: 0xff4500,
        metalness: 0.3,
        roughness: 0.4,
    });
    ball = new THREE.Mesh(geometry, material);
    ball.castShadow = true;
    ball.receiveShadow = true;
    ball.position.copy(startPosition);
    scene.add(ball);

    // Cannon.js 物理体
    const shape = new CANNON.Sphere(radius);
    ballBody = new CANNON.Body({
        mass: 1,
        position: new CANNON.Vec3(startPosition.x, startPosition.y, startPosition.z),
        shape: shape,
        material: new CANNON.Material({ friction: 0.5, restitution: 0.2 }),
        linearDamping: 0.7,
        angularDamping: 0.7
    });
    world.addBody(ballBody);
}

// 创建复杂轨道
function createTracks() {
    // 材质
    const trackMaterial = new THREE.MeshStandardMaterial({ 
        color: 0x808080,
        metalness: 0.4,
        roughness: 0.5
    });
    
    // 障碍物材质
    const obstacleMaterial = new THREE.MeshStandardMaterial({ 
        color: 0x8B4513,
        metalness: 0.2,
        roughness: 0.8
    });

    // 检查点材质
    const checkpointMaterial = new THREE.MeshStandardMaterial({
        color: 0x00ff00,
        transparent: true,
        opacity: 0.5
    });

    // 物理材质
    const groundPhysMaterial = new CANNON.Material();

    // 起始平台
    createPlatform(0, 0, 0, 10, 1, 10, trackMaterial, groundPhysMaterial);
    
    // 第一个检查点
    addCheckpoint(0, 1.5, 0);

    // 斜坡
    createPlatform(0, -2, -10, 4, 1, 15, trackMaterial, groundPhysMaterial, 0, Math.PI/12, 0);
    
    // 第二个平台
    createPlatform(0, -6, -20, 8, 1, 8, trackMaterial, groundPhysMaterial);
    
    // 第二个检查点
    addCheckpoint(0, -4.5, -20);

    // 弯曲轨道 - 向右
    createPlatform(8, -6, -24, 10, 1, 4, trackMaterial, groundPhysMaterial);
    
    // 障碍物
    createObstacle(8, -5, -24, 1, 1, 1, obstacleMaterial);
    createObstacle(12, -5, -24, 1, 1, 1, obstacleMaterial);
    
    // 向下的螺旋轨道 - 第一段
    createPlatform(18, -6, -24, 4, 1, 10, trackMaterial, groundPhysMaterial, 0, 0, -Math.PI/12);
    
    // 第三个检查点
    addCheckpoint(18, -4.5, -24);
    
    // 向下的螺旋轨道 - 第二段
    createPlatform(22, -8, -18, 4, 1, 10, trackMaterial, groundPhysMaterial, 0, 0, -Math.PI/6);
    
    // 向下的螺旋轨道 - 第三段
    createPlatform(20, -12, -10, 4, 1, 10, trackMaterial, groundPhysMaterial, 0, 0, Math.PI/4);
    
    // 底部平台
    createPlatform(12, -15, -6, 10, 1, 10, trackMaterial, groundPhysMaterial);
    
    // 第四个检查点
    addCheckpoint(12, -13.5, -6);
    
    // 上升轨道
    createPlatform(2, -13, -6, 10, 1, 4, trackMaterial, groundPhysMaterial, 0, Math.PI/12, 0);
    
    // 窄桥
    createPlatform(-8, -10, -6, 10, 1, 2, trackMaterial, groundPhysMaterial);
    
    // 障碍物
    createObstacle(-8, -9, -6, 1, 1, 1, obstacleMaterial);
    createObstacle(-12, -9, -6, 1, 1, 1, obstacleMaterial);
    
    // 最终平台
    createPlatform(-18, -10, -6, 6, 1, 6, trackMaterial, groundPhysMaterial);
    
    // 最终检查点
    addCheckpoint(-18, -8.5, -6);

    // 创建边界墙，防止小球掉出场景
    createBoundaryWalls();
}

// 创建平台函数
function createPlatform(x, y, z, width, height, depth, material, physMaterial, rotX = 0, rotY = 0, rotZ = 0) {
    // Three.js 几何体
    const geometry = new THREE.BoxGeometry(width, height, depth);
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(x, y, z);
    mesh.rotation.set(rotX, rotY, rotZ);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    scene.add(mesh);
    tracks.push(mesh);

    // Cannon.js 物理体
    const halfExtents = new CANNON.Vec3(width/2, height/2, depth/2);
    const boxShape = new CANNON.Box(halfExtents);
    const boxBody = new CANNON.Body({
        mass: 0, // 静态物体
        position: new CANNON.Vec3(x, y, z),
        shape: boxShape,
        material: physMaterial
    });
    
    // 设置旋转
    boxBody.quaternion.setFromEuler(rotX, rotY, rotZ);
    
    world.addBody(boxBody);
    trackBodies.push(boxBody);
}

// 创建障碍物
function createObstacle(x, y, z, width, height, depth, material) {
    // Three.js 几何体
    const geometry = new THREE.BoxGeometry(width, height, depth);
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(x, y, z);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    scene.add(mesh);
    tracks.push(mesh);

    // Cannon.js 物理体
    const halfExtents = new CANNON.Vec3(width/2, height/2, depth/2);
    const boxShape = new CANNON.Box(halfExtents);
    const boxBody = new CANNON.Body({
        mass: 0, // 静态物体
        position: new CANNON.Vec3(x, y, z),
        shape: boxShape
    });
    world.addBody(boxBody);
    trackBodies.push(boxBody);
}

// 添加检查点
function addCheckpoint(x, y, z) {
    const geometry = new THREE.CylinderGeometry(2, 2, 0.2, 32);
    const material = new THREE.MeshStandardMaterial({
        color: 0x00ff00,
        transparent: true,
        opacity: 0.3
    });
    const checkpoint = new THREE.Mesh(geometry, material);
    checkpoint.position.set(x, y, z);
    checkpoint.rotation.x = Math.PI / 2;
    scene.add(checkpoint);
    
    checkpoints.push({
        position: new THREE.Vector3(x, y, z),
        mesh: checkpoint
    });
}

// 创建边界墙
function createBoundaryWalls() {
    // 创建一个大的包围盒，防止小球掉出场景
    const wallMaterial = new THREE.MeshStandardMaterial({
        color: 0x808080,
        transparent: true,
        opacity: 0.2,
        side: THREE.DoubleSide
    });
    
    // 底部边界 - 比所有轨道都低
    createPlatform(0, -30, 0, 100, 1, 100, wallMaterial, new CANNON.Material());
}

// 窗口大小调整函数
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 键盘按下事件
function onKeyDown(event) {
    keys[event.code] = true;
    
    // 按R键重置游戏
    if (event.code === 'KeyR') {
        restartGame();
    }
    
    // 空格键跳跃
    if (event.code === 'Space' && canJump) {
        ballBody.velocity.y = 4; // 从7降低到4，减小跳跃高度
        canJump = false;
    }
}

// 键盘松开事件
function onKeyUp(event) {
    keys[event.code] = false;
}

// 重启游戏
function restartGame() {
    ballBody.position.copy(new CANNON.Vec3(startPosition.x, startPosition.y, startPosition.z));
    ballBody.velocity.set(0, 0, 0);
    ballBody.angularVelocity.set(0, 0, 0);
    currentCheckpoint = 0;
    
    // 重置检查点颜色
    checkpoints.forEach(checkpoint => {
        checkpoint.mesh.material.color.set(0x00ff00);
    });
}

// 检查是否到达检查点
function checkCheckpoints() {
    if (currentCheckpoint < checkpoints.length) {
        const checkpoint = checkpoints[currentCheckpoint];
        const distance = new THREE.Vector3(
            ballBody.position.x,
            ballBody.position.y,
            ballBody.position.z
        ).distanceTo(checkpoint.position);
        
        if (distance < 2) {
            // 到达检查点
            checkpoint.mesh.material.color.set(0xff0000); // 变红表示已经过
            currentCheckpoint++;
            
            // 如果是最后一个检查点
            if (currentCheckpoint === checkpoints.length) {
                alert("恭喜！你完成了所有关卡！");
                setTimeout(restartGame, 1000);
            }
        }
    }
}

// 检测小球是否掉落
function checkBallFall() {
    if (ballBody.position.y < -20) {
        // 如果有检查点，回到最近的检查点
        if (currentCheckpoint > 0) {
            const lastCheckpoint = checkpoints[currentCheckpoint - 1];
            ballBody.position.copy(new CANNON.Vec3(
                lastCheckpoint.position.x,
                lastCheckpoint.position.y + 2, // 稍微高一点，防止卡住
                lastCheckpoint.position.z
            ));
            ballBody.velocity.set(0, 0, 0);
            ballBody.angularVelocity.set(0, 0, 0);
        } else {
            // 否则回到起点
            restartGame();
        }
    }
}

// 移动小球
function moveBall() {
    const strength = 5; // 从20降低到5
    const damping = 0.3; // 从0.95降低到0.3
    
    // 检测小球是否接触地面（用于跳跃检测）
    const rayStart = new CANNON.Vec3(ballBody.position.x, ballBody.position.y, ballBody.position.z);
    const rayEnd = new CANNON.Vec3(ballBody.position.x, ballBody.position.y - 0.6, ballBody.position.z);
    const ray = new CANNON.Ray(rayStart, rayEnd);
    const result = new CANNON.RaycastResult();
    ray.intersectWorld(world, { result: result });
    
    if (result.hasHit) {
        canJump = true;
    }
    
    // 根据按键移动小球
    if (keys['ArrowUp'] || keys['KeyW']) {
        ballBody.applyImpulse(
            new CANNON.Vec3(0, 0, -strength * damping),
            new CANNON.Vec3(0, 0, 0)
        );
    }
    if (keys['ArrowDown'] || keys['KeyS']) {
        ballBody.applyImpulse(
            new CANNON.Vec3(0, 0, strength * damping),
            new CANNON.Vec3(0, 0, 0)
        );
    }
    if (keys['ArrowLeft'] || keys['KeyA']) {
        ballBody.applyImpulse(
            new CANNON.Vec3(-strength * damping, 0, 0),
            new CANNON.Vec3(0, 0, 0)
        );
    }
    if (keys['ArrowRight'] || keys['KeyD']) {
        ballBody.applyImpulse(
            new CANNON.Vec3(strength * damping, 0, 0),
            new CANNON.Vec3(0, 0, 0)
        );
    }
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);
    
    const delta = clock.getDelta();
    
    // 更新物理世界
    world.step(1/60);
    
    // 更新小球位置
    ball.position.copy(ballBody.position);
    ball.quaternion.copy(ballBody.quaternion);
    
    // 移动小球
    moveBall();
    
    // 检查检查点
    checkCheckpoints();
    
    // 检查小球是否掉落
    checkBallFall();
    
    // 更新相机位置，跟随小球
    const cameraOffset = new THREE.Vector3(0, 5, 10);
    const cameraTarget = new THREE.Vector3().copy(ball.position).add(cameraOffset);
    camera.position.lerp(cameraTarget, 0.05);
    camera.lookAt(ball.position);
    
    // 更新控制器
    controls.update();
    
    // 渲染场景
    renderer.render(scene, camera);
}

// 页面加载完成后初始化
window.addEventListener('load', init);
