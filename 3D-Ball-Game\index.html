<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D 小球滚动模型</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            display: block;
        }
        #info {
            position: absolute;
            top: 10px;
            width: 100%;
            text-align: center;
            color: white;
            pointer-events: none;
        }
        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
        }
        #restart {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #restart:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div id="info">3D 小球滚动模型 - 使用方向键或 WASD 控制小球</div>
    <div id="controls">
        控制: 方向键 或 WASD<br>
        重置: R 键<br>
        跳跃: 空格键
    </div>
    <button id="restart">重新开始</button>

    <!-- 引入Three.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>
    
    <!-- 引入Cannon.js物理引擎 -->
    <script src="https://cdn.jsdelivr.net/npm/cannon@0.6.2/build/cannon.min.js"></script>
    
    <!-- 引入我们的游戏脚本 -->
    <script src="js/game.js"></script>
</body>
</html>
