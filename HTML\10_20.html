
<html xmlns="http://www.w3.org/1999/xhtml" >
    <head runat="server">
        <title>无标题页</title>
    <script language="javascript" type="text/javascript">
    // <!CDATA[
    
    function Button1_onclick() {
    //document.getElementById("content").innerHTML = "ddd"; 
    document.getElementById("content").value = "sss";  
    }
    function Button2_onclick() {
    document.getElementById("content").innerHTML = "ddd";  
    }
    
    // ]]>
    </script>
    </head>
    <body>
        <form id="form1" runat="server">
        <div>
            <textarea id="content" cols="20" rows="2"></textarea>
            <input type="button" value="添加新闻1" onclick="document.getElementById('content').value = 'dddsss'"/>  
            <input id="Button1" value="添加新闻2" type="button" onclick="return Button1_onclick()" />
            <input id="Button2" value="添加新闻3" type="button" onclick="return Button2_onclick()" />
        </div>
        </form>
    </body>
    </html>
    