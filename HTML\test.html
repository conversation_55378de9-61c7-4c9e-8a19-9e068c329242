

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 设置表格无边框 */
        table {
            border-collapse: collapse;
        }

        /* 设置表格中单元格无边框 */
        td, th {
            border: none;
            padding: 8px; /* 可选：添加内边距以提高可读性 */
        }
    </style>
    <title>无边框表格</title>
</head>
<body>
    <table>
        <tr>
            <th>Header 1</th>
            <th>Header 2</th>
            <th>Header 3</th>
        </tr>
        <tr>
            <td>Data 1</td>
            <td>Data 2</td>
            <td>Data 3</td>
        </tr>
        <!-- 添加更多行... -->
    </table>
</body>
</html>